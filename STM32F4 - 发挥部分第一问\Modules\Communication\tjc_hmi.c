/**
  ******************************************************************************
  * @file    tjc_hmi.c
  * <AUTHOR>
  * @version V1.0
  * @date    2024-08-02
  * @brief   陶晶池串口屏驱动实现文件
  ******************************************************************************
  * @attention
  *
  * 本文件实现陶晶池串口屏的通信功能
  *
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "tjc_hmi.h"
#include "../Control/ad9910_control.h"
#include "../Control/gain_calculator.h"
#include "../Core/systick.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <math.h>
#include <ctype.h>

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
#define TJC_EVENT_QUEUE_SIZE        8       ///< 事件队列大小
#define TJC_CMD_TIMEOUT             100     ///< 命令超时时间 (ms)

/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/

/* 串口状态变量 */
static bool tjc_initialized = false;
static TJC_EventCallback_t event_callback = NULL;
static uint8_t current_page = 0;

/* 接收缓冲区和状态 */
static uint8_t rx_buffer[TJC_RX_BUFFER_SIZE];
static uint16_t rx_index = 0;
static bool rx_complete = false;

/* 事件队列 */
static TJC_Event_t event_queue[TJC_EVENT_QUEUE_SIZE];
static uint8_t event_queue_head = 0;
static uint8_t event_queue_tail = 0;
static uint8_t event_queue_count = 0;

/* ==================== 电赛G题专用状态变量 ==================== */

/* 主控制界面状态 */
static uint32_t input_frequency_mhz = 5;       ///< 输入频率值 (MHz)
static uint16_t input_amplitude_mv = 500;      ///< 输入幅度值 (mV)
static uint32_t current_frequency_hz = 5000000; ///< 当前输出频率 (Hz)
static uint16_t current_amplitude_mv = 500;    ///< 当前输出幅度 (mV)

/* 电路识别界面状态 */
static uint32_t sweep_start_khz = 1;           ///< 扫频起始频率 (kHz)
static uint32_t sweep_end_khz = 100;           ///< 扫频结束频率 (kHz)
static uint16_t sweep_points = 50;             ///< 扫频点数
static uint16_t sweep_time_ms = 100;           ///< 步进时间 (ms)
static bool identify_active = false;           ///< 识别进行中标志

/* 界面控制状态 */
// current_page已在上面定义，这里删除重复定义

/* 输入控制变量 */
static bool measurement_active = false;       ///< 测量进行中标志
static uint16_t current_voltage_mv = 0;       ///< 当前电压显示值 (mV)
static uint32_t input_number = 0;             ///< 数字输入值
static char input_unit[16] = "Hz";            ///< 单位输入字符串
static uint32_t n0_input_value = 0;           ///< n0控件输入值
static char t0_input_unit[16] = "Hz";         ///< t0控件单位字符串

/* 避免编译器警告的宏 */
#define UNUSED(x) ((void)(x))

/* Private function prototypes -----------------------------------------------*/
static void TJC_UART_Init(void);
static void TJC_SendByte(uint8_t byte);
static void TJC_SendString(const char* str);
static void TJC_SendEndSequence(void);
static void TJC_ProcessReceivedData(void);
static void TJC_ParseTouchEvent(uint8_t* data, uint16_t length);
static void TJC_ParseNumberInput(uint8_t* data, uint16_t length);
static void TJC_ParseTextInput(uint8_t* data, uint16_t length);
static void TJC_AddEvent(TJC_Event_t* event);
static uint32_t TJC_GetTick(void);
static void TJC_ParseUnitString(const char* unit_str, char* normalized_unit);
static uint32_t TJC_ConvertToHz(uint32_t value, const char* unit);

/* Private functions ---------------------------------------------------------*/

/**
 * @brief  陶晶池串口屏初始化
 * @param  None
 * @retval None
 */
void TJC_HMI_Init(void)
{
    /* 串口初始化 */
    TJC_UART_Init();
    
    /* 初始化状态变量 */
    tjc_initialized = true;
    current_page = 0;
    rx_index = 0;
    rx_complete = false;
    measurement_active = false;

    /* 清空事件队列 */
    event_queue_head = 0;
    event_queue_tail = 0;
    event_queue_count = 0;
    memset(event_queue, 0, sizeof(event_queue));

    /* 初始化参数 */
    current_frequency_hz = 0;
    current_voltage_mv = 0;
    input_number = 0;
    strcpy(input_unit, "Hz");

    /* 短暂延时，等待串口屏启动 */
    Delay_ms(500);

    /* 发送初始化命令 */
    TJC_HMI_SendCommand("bkcmd=3");  // 设置返回数据格式
    Delay_ms(10);
}

/**
 * @brief  串口初始化
 * @param  None
 * @retval None
 */
static void TJC_UART_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    USART_InitTypeDef USART_InitStructure;
    NVIC_InitTypeDef NVIC_InitStructure;

    /* 使能时钟 */
    RCC_AHB1PeriphClockCmd(TJC_USART_TX_RCC, ENABLE);
    RCC_APB1PeriphClockCmd(TJC_USART_RCC, ENABLE);  // USART2在APB1总线上

    /* 配置GPIO复用功能 */
    GPIO_PinAFConfig(TJC_USART_TX_PORT, TJC_USART_TX_AF_PIN, TJC_USART_AF);
    GPIO_PinAFConfig(TJC_USART_RX_PORT, TJC_USART_RX_AF_PIN, TJC_USART_AF);

    /* 配置TX引脚 */
    GPIO_StructInit(&GPIO_InitStructure);
    GPIO_InitStructure.GPIO_Pin = TJC_USART_TX_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_100MHz;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
    GPIO_Init(TJC_USART_TX_PORT, &GPIO_InitStructure);

    /* 配置RX引脚 */
    GPIO_InitStructure.GPIO_Pin = TJC_USART_RX_PIN;
    GPIO_Init(TJC_USART_RX_PORT, &GPIO_InitStructure);

    /* 配置USART */
    USART_DeInit(TJC_USART);
    USART_StructInit(&USART_InitStructure);
    USART_InitStructure.USART_BaudRate = TJC_BAUD_RATE;
    USART_InitStructure.USART_WordLength = USART_WordLength_8b;
    USART_InitStructure.USART_StopBits = USART_StopBits_1;
    USART_InitStructure.USART_Parity = USART_Parity_No;
    USART_InitStructure.USART_Mode = USART_Mode_Rx | USART_Mode_Tx;
    USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
    USART_Init(TJC_USART, &USART_InitStructure);

    /* 清除标志位 */
    USART_ClearFlag(TJC_USART, USART_FLAG_RXNE);

    /* 使能串口 */
    USART_Cmd(TJC_USART, ENABLE);

    /* 使能接收中断 */
    USART_ITConfig(TJC_USART, USART_IT_RXNE, ENABLE);

    /* 配置中断 */
    NVIC_InitStructure.NVIC_IRQChannel = USART2_IRQn;  // 使用USART2中断
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);
}

/**
 * @brief  发送单个字节
 * @param  byte: 要发送的字节
 * @retval None
 */
static void TJC_SendByte(uint8_t byte)
{
    USART_SendData(TJC_USART, byte);
    while (RESET == USART_GetFlagStatus(TJC_USART, USART_FLAG_TXE)) {}
}

/**
 * @brief  发送字符串
 * @param  str: 要发送的字符串
 * @retval None
 */
static void TJC_SendString(const char* str)
{
    while (*str) {
        TJC_SendByte(*str++);
    }
}

/**
 * @brief  发送命令到陶晶池串口屏
 * @param  cmd: 命令字符串
 * @retval None
 */
void TJC_HMI_SendCommand(const char* cmd)
{
    if (!tjc_initialized || cmd == NULL) return;
    
    /* 发送命令内容 */
    TJC_SendString(cmd);
    
    /* 发送结束符 */
    TJC_SendByte(0xFF);
    TJC_SendByte(0xFF);
    TJC_SendByte(0xFF);
}

/**
 * @brief  设置文本控件内容
 * @param  obj_name: 控件名称
 * @param  text: 文本内容
 * @retval None
 */
void TJC_HMI_SetText(const char* obj_name, const char* text)
{
    char cmd[128];
    snprintf(cmd, sizeof(cmd), "%s.txt=\"%s\"", obj_name, text);
    TJC_HMI_SendCommand(cmd);
}

/**
 * @brief  设置数值控件内容
 * @param  obj_name: 控件名称
 * @param  value: 数值
 * @retval None
 */
void TJC_HMI_SetValue(const char* obj_name, int32_t value)
{
    char cmd[64];
    snprintf(cmd, sizeof(cmd), "%s.val=%ld", obj_name, value);
    TJC_HMI_SendCommand(cmd);
}

/**
 * @brief  设置进度条/滑块值
 * @param  obj_name: 控件名称
 * @param  value: 进度值 (0-100)
 * @retval None
 */
void TJC_HMI_SetProgress(const char* obj_name, uint8_t value)
{
    if (value > 100) value = 100;
    TJC_HMI_SetValue(obj_name, value);
}

/**
 * @brief  切换页面
 * @param  page_id: 页面ID
 * @retval None
 */
void TJC_HMI_ChangePage(uint8_t page_id)
{
    char cmd[32];
    snprintf(cmd, sizeof(cmd), "page %d", page_id);
    TJC_HMI_SendCommand(cmd);
}

/**
 * @brief  设置控件可见性
 * @param  obj_name: 控件名称
 * @param  visible: true-可见, false-隐藏
 * @retval None
 */
void TJC_HMI_SetVisible(const char* obj_name, bool visible)
{
    char cmd[64];
    snprintf(cmd, sizeof(cmd), "vis %s,%d", obj_name, visible ? 1 : 0);
    TJC_HMI_SendCommand(cmd);
}

/**
 * @brief  设置控件颜色
 * @param  obj_name: 控件名称
 * @param  color: 颜色值 (RGB565)
 * @retval None
 */
void TJC_HMI_SetColor(const char* obj_name, uint16_t color)
{
    char cmd[64];
    snprintf(cmd, sizeof(cmd), "%s.bco=%d", obj_name, color);
    TJC_HMI_SendCommand(cmd);
}

/**
 * @brief  陶晶池串口屏处理 (主循环调用)
 * @param  None
 * @retval None
 */
void TJC_HMI_Process(void)
{
    if (!tjc_initialized) return;
    
    /* 处理接收到的数据 */
    if (rx_complete) {
        TJC_ProcessReceivedData();
        rx_complete = false;
        rx_index = 0;
    }
}

/**
 * @brief  处理接收到的数据
 * @param  None
 * @retval None
 */
static void TJC_ProcessReceivedData(void)
{
    if (rx_index < 3) return; // 至少需要3个字节的结束符

    /* 检查数据类型并解析 */
    if (rx_buffer[0] == 0x65) {
        /* 触摸事件 */
        TJC_ParseTouchEvent(rx_buffer, rx_index);
    } else if (rx_buffer[0] == 0x71) {
        /* 数字键盘输入事件 */
        TJC_ParseNumberInput(rx_buffer, rx_index);
    } else if (rx_buffer[0] == 0x70) {
        /* 文本输入事件 */
        TJC_ParseTextInput(rx_buffer, rx_index);
    } else if (rx_index >= 4 && rx_buffer[0] == 0x1A) {
        /* 特殊事件 (printh 31) */
        if (rx_buffer[1] == 31) {
            TJC_HMI_StartMeasurement();
        }
    }
}

/**
 * @brief  解析触摸事件
 * @param  data: 数据缓冲区
 * @param  length: 数据长度
 * @retval None
 */
static void TJC_ParseTouchEvent(uint8_t* data, uint16_t length)
{
    if (length < 7) return; // 触摸事件至少7个字节

    TJC_Event_t event;
    event.type = (data[1] == 0x01) ? TJC_EVENT_TOUCH_PRESS : TJC_EVENT_TOUCH_RELEASE;
    event.page_id = data[2];
    event.component_id = data[3];
    event.value = 0;
    memset(event.text, 0, sizeof(event.text));

    /* 处理特定控件的触摸事件 */
    if (event.type == TJC_EVENT_TOUCH_PRESS) {
        if (event.component_id == TJC_ID_B0) {
            /* b0按钮按下 */
            TJC_HMI_HandleMeasureButton(event.page_id);
        }
    }

    /* 添加到事件队列 */
    TJC_AddEvent(&event);
}

/**
 * @brief  解析数字键盘输入事件
 * @param  data: 数据缓冲区
 * @param  length: 数据长度
 * @retval None
 */
static void TJC_ParseNumberInput(uint8_t* data, uint16_t length)
{
    if (length < 8) return; // 数字输入事件至少8个字节

    /* 提取数值 (小端格式) */
    uint32_t value = (uint32_t)data[1] |
                    ((uint32_t)data[2] << 8) |
                    ((uint32_t)data[3] << 16) |
                    ((uint32_t)data[4] << 24);

    /* 处理数字输入 */
    TJC_HMI_HandleNumberInput(value);
}

/**
 * @brief  解析文本输入事件
 * @param  data: 数据缓冲区
 * @param  length: 数据长度
 * @retval None
 */
static void TJC_ParseTextInput(uint8_t* data, uint16_t length)
{
    if (length < 4) return; // 至少需要头部和结束符

    /* 提取文本内容 (去除头部和结束符) */
    char text_buffer[32];
    uint16_t text_length = length - 4; // 去除头部1字节和结束符3字节
    if (text_length > sizeof(text_buffer) - 1) {
        text_length = sizeof(text_buffer) - 1;
    }

    memcpy(text_buffer, &data[1], text_length);
    text_buffer[text_length] = '\0';

    /* 处理文本输入 */
    TJC_HMI_HandleUnitInput(text_buffer);
}

/**
 * @brief  添加事件到队列
 * @param  event: 事件指针
 * @retval None
 */
static void TJC_AddEvent(TJC_Event_t* event)
{
    if (event_queue_count >= TJC_EVENT_QUEUE_SIZE) {
        return; // 队列满
    }
    
    event_queue[event_queue_tail] = *event;
    event_queue_tail = (event_queue_tail + 1) % TJC_EVENT_QUEUE_SIZE;
    event_queue_count++;
    
    /* 调用回调函数 */
    if (event_callback != NULL) {
        event_callback(event);
    }
}

/**
 * @brief  解析单位字符串 (处理大小写不敏感)
 * @param  unit_str: 输入的单位字符串
 * @param  normalized_unit: 标准化后的单位字符串
 * @retval None
 */
static void TJC_ParseUnitString(const char* unit_str, char* normalized_unit)
{
    char temp[16];
    int i;

    /* 转换为小写 */
    for (i = 0; i < strlen(unit_str) && i < 15; i++) {
        temp[i] = (unit_str[i] >= 'A' && unit_str[i] <= 'Z') ?
                  (unit_str[i] + 32) : unit_str[i];
    }
    temp[i] = '\0';

    /* 识别频率单位 */
    if (strstr(temp, "hz") != NULL) {
        if (strstr(temp, "mhz") != NULL || strstr(temp, "m") != NULL) {
            strcpy(normalized_unit, "MHz");
        } else if (strstr(temp, "khz") != NULL || strstr(temp, "k") != NULL) {
            strcpy(normalized_unit, "kHz");
        } else {
            strcpy(normalized_unit, "Hz");
        }
    }
    /* 识别电压单位 */
    else if (strstr(temp, "v") != NULL) {
        if (strstr(temp, "mv") != NULL || strstr(temp, "m") != NULL) {
            strcpy(normalized_unit, "mV");
        } else {
            strcpy(normalized_unit, "V");
        }
    }
    /* 默认为Hz */
    else {
        strcpy(normalized_unit, "Hz");
    }
}

/**
 * @brief  将数值和单位转换为Hz
 * @param  value: 数值
 * @param  unit: 单位
 * @retval 转换后的Hz值
 */
static uint32_t TJC_ConvertToHz(uint32_t value, const char* unit)
{
    /* 大小写不敏感的单位比较 */
    if (strcasecmp(unit, "MHZ") == 0 || strcasecmp(unit, "MHz") == 0) {
        return value * 1000000;
    } else if (strcasecmp(unit, "KHZ") == 0 || strcasecmp(unit, "kHz") == 0) {
        return value * 1000;
    } else {
        return value; // Hz (默认)
    }
}

/**
 * @brief  获取系统时间戳
 * @param  None
 * @retval 时间戳 (ms)
 */
static uint32_t TJC_GetTick(void)
{
    return SysTick_GetTick(); // 使用正确的函数名
}

/**
 * @brief  获取系统运行时间 (对外接口)
 * @param  None
 * @retval 时间戳 (ms)
 */
uint32_t TJC_HMI_GetSystemTime(void)
{
    return TJC_GetTick(); // 使用内部函数，避免未使用警告
}

/**
 * @brief  设置AD9910输出幅度 (通过串口屏控制)
 * @param  amplitude_mv: 目标幅度 (mV)
 * @retval None
 */
void TJC_HMI_SetAD9910Amplitude(uint16_t amplitude_mv)
{
    ControlStatus_t status = AD9910_Control_SetTargetAmplitude(amplitude_mv);
    if (status == CTRL_STATUS_OK) {
        /* 更新显示 */
        TJC_HMI_SetCircuitVoltage(amplitude_mv);

        /* 更新频率显示 (可能因增益变化而改变) */
        SystemParams_t params;
        if (AD9910_Control_GetParams(&params) == CTRL_STATUS_OK) {
            TJC_HMI_SetOutputFrequency(params.frequency_hz);
        }
    }
}

/**
 * @brief  注册事件回调函数
 * @param  callback: 回调函数指针
 * @retval None
 */
void TJC_HMI_RegisterCallback(TJC_EventCallback_t callback)
{
    event_callback = callback;
}

/**
 * @brief  获取是否有新事件
 * @param  event: 事件输出
 * @retval true-有事件, false-无事件
 */
bool TJC_HMI_HasEvent(TJC_Event_t* event)
{
    if (event_queue_count == 0) {
        return false;
    }
    
    if (event != NULL) {
        *event = event_queue[event_queue_head];
    }
    
    event_queue_head = (event_queue_head + 1) % TJC_EVENT_QUEUE_SIZE;
    event_queue_count--;
    
    return true;
}

/**
 * @brief  清空事件队列
 * @param  None
 * @retval None
 */
void TJC_HMI_ClearEvents(void)
{
    event_queue_head = 0;
    event_queue_tail = 0;
    event_queue_count = 0;
    memset(event_queue, 0, sizeof(event_queue));
}

/**
 * @brief  串口中断处理函数
 * @param  None
 * @retval None
 */
void TJC_HMI_UART_IRQHandler(void)
{
    if (USART_GetITStatus(TJC_USART, USART_IT_RXNE) != RESET) {
        uint8_t received_byte = USART_ReceiveData(TJC_USART);
        
        if (rx_index < TJC_RX_BUFFER_SIZE - 1) {
            rx_buffer[rx_index++] = received_byte;
            
            /* 检查是否收到完整命令 (以0xFF 0xFF 0xFF结尾) */
            if (rx_index >= 3 && 
                rx_buffer[rx_index-3] == 0xFF && 
                rx_buffer[rx_index-2] == 0xFF && 
                rx_buffer[rx_index-1] == 0xFF) {
                rx_complete = true;
            }
        } else {
            /* 缓冲区溢出，重新开始 */
            rx_index = 0;
        }
    }
}

/* printf重定向到陶晶池串口屏 */
#if !defined(__MICROLIB)
#if (__ARMCLIB_VERSION <= 6000000)
struct __FILE {
    int handle;
};
#endif

FILE __stdout;

void _sys_exit(int x) {
    x = x;
}
#endif

int fputc(int ch, FILE *f)
{
    USART_SendData(TJC_USART, (uint8_t)ch);
    while (RESET == USART_GetFlagStatus(TJC_USART, USART_FLAG_TXE)) {}
    return ch;
}

/* ==================== AD9910专用显示函数 ==================== */

/* ==================== 电赛G题专用HMI接口函数实现 ==================== */

/**
 * @brief  初始化HMI并显示主控制界面
 * @param  None
 * @retval None
 */
void TJC_HMI_ShowMainPage(void)
{
    /* 切换到主控制页面 */
    TJC_SendString("page 0");
    TJC_SendEndSequence();
    current_page = TJC_PAGE_MAIN;
    Delay_ms(100);

    /* 设置标题 */
    TJC_HMI_SetText(TJC_CTRL_TITLE, "AD9910 Signal Generator");

    /* 设置单位显示 */
    TJC_HMI_SetText(TJC_CTRL_FREQ_UNIT, "MHz");
    TJC_HMI_SetText(TJC_CTRL_AMP_UNIT, "mV");

    /* 设置默认输入值 */
    TJC_HMI_SetValue(TJC_CTRL_FREQ_INPUT, input_frequency_mhz);
    TJC_HMI_SetValue(TJC_CTRL_AMP_INPUT, input_amplitude_mv);

    /* 更新当前输出显示 */
    TJC_HMI_SetCurrentFrequency(current_frequency_hz);
    TJC_HMI_SetCurrentAmplitude(current_amplitude_mv);

    Delay_ms(500);
}

/**
 * @brief  显示电路识别界面
 * @param  None
 * @retval None
 */
void TJC_HMI_ShowIdentifyPage(void)
{
    /* 切换到电路识别页面 */
    TJC_SendString("page 1");
    TJC_SendEndSequence();
    current_page = TJC_PAGE_IDENTIFY;
    Delay_ms(100);

    /* 设置标题 */
    TJC_HMI_SetText(TJC_CTRL_ID_TITLE, "电路模型识别");

    /* 设置单位显示 */
    TJC_HMI_SetText(TJC_CTRL_SWEEP_UNIT, "kHz");

    /* 设置默认扫频参数 */
    TJC_HMI_SetValue(TJC_CTRL_SWEEP_START, sweep_start_khz);
    TJC_HMI_SetValue(TJC_CTRL_SWEEP_END, sweep_end_khz);
    TJC_HMI_SetValue(TJC_CTRL_SWEEP_POINTS, sweep_points);
    TJC_HMI_SetValue(TJC_CTRL_SWEEP_TIME, sweep_time_ms);

    /* 初始化状态显示 */
    TJC_HMI_SetIdentifyStatus("Waiting...");
    TJC_HMI_SetCircuitType("Unknown");
    TJC_HMI_SetModelParams("--");

    Delay_ms(500);
}

/**
 * @brief  设置当前频率显示
 * @param  frequency_hz: 频率值 (Hz)
 * @retval None
 */
void TJC_HMI_SetCurrentFrequency(uint32_t frequency_hz)
{
    char freq_str[32];

    current_frequency_hz = frequency_hz;

    /* 智能格式化频率显示 */
    if (frequency_hz >= 1000000) {
        /* MHz显示 */
        double freq_mhz = (double)frequency_hz / 1000000.0;
        snprintf(freq_str, sizeof(freq_str), "当前输出: %.3f MHz", freq_mhz);
    } else if (frequency_hz >= 1000) {
        /* kHz显示 */
        double freq_khz = (double)frequency_hz / 1000.0;
        snprintf(freq_str, sizeof(freq_str), "当前输出: %.1f kHz", freq_khz);
    } else {
        /* Hz显示 */
        snprintf(freq_str, sizeof(freq_str), "当前输出: %lu Hz", frequency_hz);
    }

    /* 更新显示 */
    TJC_HMI_SetText(TJC_CTRL_FREQ_CURRENT, freq_str);
}

/**
 * @brief  设置当前幅度显示
 * @param  amplitude_mv: 幅度值 (mV)
 * @retval None
 */
void TJC_HMI_SetCurrentAmplitude(uint16_t amplitude_mv)
{
    char amp_str[32];

    current_amplitude_mv = amplitude_mv;

    /* 智能格式化幅度显示 */
    if (amplitude_mv >= 1000) {
        /* V显示 */
        double amp_v = (double)amplitude_mv / 1000.0;
        snprintf(amp_str, sizeof(amp_str), "当前输出: %.2f V", amp_v);
    } else {
        /* mV显示 */
        snprintf(amp_str, sizeof(amp_str), "当前输出: %d mV", amplitude_mv);
    }

    /* 更新显示 */
    TJC_HMI_SetText(TJC_CTRL_AMP_CURRENT, amp_str);
}

/**
 * @brief  设置识别状态显示
 * @param  status: 状态文本
 * @retval None
 */
void TJC_HMI_SetIdentifyStatus(const char* status)
{
    char status_str[64];
    snprintf(status_str, sizeof(status_str), "Status: %s", status);
    TJC_HMI_SetText(TJC_CTRL_ID_STATUS, status_str);
}

/**
 * @brief  设置电路类型显示
 * @param  circuit_type: 电路类型文本
 * @retval None
 */
void TJC_HMI_SetCircuitType(const char* circuit_type)
{
    char type_str[64];
    snprintf(type_str, sizeof(type_str), "电路类型: %s", circuit_type);
    TJC_HMI_SetText(TJC_CTRL_CIRCUIT_TYPE, type_str);
}

/**
 * @brief  设置模型参数显示
 * @param  model_params: 模型参数文本
 * @retval None
 */
void TJC_HMI_SetModelParams(const char* model_params)
{
    char params_str[128];
    snprintf(params_str, sizeof(params_str), "模型参数: %s", model_params);
    TJC_HMI_SetText(TJC_CTRL_MODEL_PARAMS, params_str);
}

/* ==================== 输入处理函数 ==================== */

/**
 * @brief  处理频率输入
 * @param  frequency_value: 频率数值 (MHz)
 * @retval None
 */
void TJC_HMI_HandleFrequencyInput(uint32_t frequency_value)
{
    /* 限制频率范围 1-420 MHz */
    if (frequency_value < 1) frequency_value = 1;
    if (frequency_value > 420) frequency_value = 420;

    input_frequency_mhz = frequency_value;

    /* 更新输入控件显示 */
    TJC_HMI_SetValue(TJC_CTRL_FREQ_INPUT, frequency_value);
}

/**
 * @brief  处理幅度输入
 * @param  amplitude_value: 幅度数值 (mV)
 * @retval None
 */
void TJC_HMI_HandleAmplitudeInput(uint16_t amplitude_value)
{
    /* 限制幅度范围 10-5000 mV */
    if (amplitude_value < 10) amplitude_value = 10;
    if (amplitude_value > 5000) amplitude_value = 5000;

    input_amplitude_mv = amplitude_value;

    /* 更新输入控件显示 */
    TJC_HMI_SetValue(TJC_CTRL_AMP_INPUT, amplitude_value);
}

/* ==================== 按钮处理函数 ==================== */

/**
 * @brief  处理应用设置按钮
 * @param  None
 * @retval None
 */
void TJC_HMI_HandleApplySettings(void)
{
    /* 计算实际频率 (Hz) */
    uint32_t target_frequency_hz = input_frequency_mhz * 1000000;

    /* 设置AD9910频率和幅度 */
    if (target_frequency_hz >= 1 && target_frequency_hz <= 420000000) {
        ControlStatus_t status = AD9910_Control_SetFrequency(target_frequency_hz);
        if (status == CTRL_STATUS_OK) {
            status = AD9910_Control_SetTargetAmplitude(input_amplitude_mv);
            if (status == CTRL_STATUS_OK) {
                /* 更新当前输出显示 */
                TJC_HMI_SetCurrentFrequency(target_frequency_hz);
                TJC_HMI_SetCurrentAmplitude(input_amplitude_mv);
            }
        }
    }
}

/**
 * @brief  处理开始识别按钮
 * @param  None
 * @retval None
 */
void TJC_HMI_HandleStartIdentify(void)
{
    /* 切换到识别界面 */
    TJC_HMI_ShowIdentifyPage();
}

/**
 * @brief  处理预设参数按钮
 * @param  None
 * @retval None
 */
void TJC_HMI_HandlePresetParams(void)
{
    /* 预设1: 5MHz / 500mV */
    input_frequency_mhz = 5;
    input_amplitude_mv = 500;

    /* 更新输入控件 */
    TJC_HMI_SetValue(TJC_CTRL_FREQ_INPUT, input_frequency_mhz);
    TJC_HMI_SetValue(TJC_CTRL_AMP_INPUT, input_amplitude_mv);

    /* 自动应用设置 */
    TJC_HMI_HandleApplySettings();
}

/**
 * @brief  处理开始扫频按钮
 * @param  None
 * @retval None
 */
void TJC_HMI_HandleStartSweep(void)
{
    if (!identify_active) {
        identify_active = true;

        /* 更新状态显示 */
        TJC_HMI_SetIdentifyStatus("Sweeping...");
        TJC_HMI_SetCircuitType("Identifying");
        TJC_HMI_SetModelParams("Calculating...");

        /* 这里调用电路识别算法 */
        /* TODO: 实现扫频和电路识别逻辑 */

        /* 模拟识别过程 */
        Delay_ms(2000);

        /* 模拟识别结果 */
        TJC_HMI_SetIdentifyStatus("Complete");
        TJC_HMI_SetCircuitType("2nd Order LPF");
        TJC_HMI_SetModelParams("H(s)=5/(1e-8*s^2+3e-4*s+1)");

        identify_active = false;
    }
}

/**
 * @brief  处理停止扫频按钮
 * @param  None
 * @retval None
 */
void TJC_HMI_HandleStopSweep(void)
{
    if (identify_active) {
        identify_active = false;

        /* 更新状态显示 */
        TJC_HMI_SetIdentifyStatus("Stopped");
        TJC_HMI_SetCircuitType("Unknown");
        TJC_HMI_SetModelParams("--");
    }
}

/**
 * @brief  处理返回主页按钮
 * @param  None
 * @retval None
 */
void TJC_HMI_HandleReturnMain(void)
{
    /* 停止识别 */
    if (identify_active) {
        TJC_HMI_HandleStopSweep();
    }

    /* 返回主控制界面 */
    TJC_HMI_ShowMainPage();
}

/* ==================== 事件处理函数 ==================== */

/**
 * @brief  处理串口屏事件
 * @param  event_code: 事件码
 * @param  param: 参数
 * @retval None
 */
void TJC_HMI_HandleEvent(uint8_t event_code, uint32_t param)
{
    switch (event_code) {
        case TJC_EVENT_APPLY_SETTINGS:
            TJC_HMI_HandleApplySettings();
            break;

        case TJC_EVENT_START_IDENTIFY:
            TJC_HMI_HandleStartIdentify();
            break;

        case TJC_EVENT_PRESET_PARAMS:
            TJC_HMI_HandlePresetParams();
            break;

        case TJC_EVENT_START_SWEEP:
            TJC_HMI_HandleStartSweep();
            break;

        case TJC_EVENT_STOP_SWEEP:
            TJC_HMI_HandleStopSweep();
            break;

        case TJC_EVENT_RETURN_MAIN:
            TJC_HMI_HandleReturnMain();
            break;

        case 20: /* 频率输入事件 */
            TJC_HMI_HandleFrequencyInput(param);
            break;

        case 21: /* 幅度输入事件 */
            TJC_HMI_HandleAmplitudeInput((uint16_t)param);
            break;

        case 22: /* 起始频率输入 */
            sweep_start_khz = param;
            break;

        case 23: /* 结束频率输入 */
            sweep_end_khz = param;
            break;

        case 24: /* 扫频点数输入 */
            sweep_points = (uint16_t)param;
            break;

        case 25: /* 步进时间输入 */
            sweep_time_ms = (uint16_t)param;
            break;

        default:
            break;
    }
}

/* ==================== 兼容性函数 ==================== */

/**
 * @brief  兼容性函数 - 显示欢迎界面
 * @param  None
 * @retval None
 */
void TJC_HMI_ShowWelcome(void)
{
    TJC_HMI_ShowMainPage();
}

/**
 * @brief  兼容性函数 - 设置输出频率显示
 * @param  frequency_hz: 频率值 (Hz)
 * @retval None
 */
void TJC_HMI_SetOutputFrequency(uint32_t frequency_hz)
{
    TJC_HMI_SetCurrentFrequency(frequency_hz);
}

/**
 * @brief  兼容性函数 - 设置电路电压输出峰峰值
 * @param  voltage_mv: 电压峰峰值 (mV)
 * @retval None
 */
void TJC_HMI_SetCircuitVoltage(uint16_t voltage_mv)
{
    TJC_HMI_SetCurrentAmplitude(voltage_mv);
}

/* ==================== 测试函数 ==================== */

/**
 * @brief  测试电赛G题HMI所有功能
 * @param  None
 * @retval None
 */
void TJC_HMI_TestAllFunctions(void)
{
    /* 初始化并显示主界面 */
    TJC_HMI_ShowMainPage();
    Delay_ms(2000);

    /* 测试频率和幅度设置 */
    TJC_HMI_HandleFrequencyInput(10);      // 设置10MHz
    TJC_HMI_HandleAmplitudeInput(300);     // 设置300mV
    TJC_HMI_HandleApplySettings();         // 应用设置
    Delay_ms(2000);

    /* 测试预设参数 */
    TJC_HMI_HandlePresetParams();          // 使用预设参数
    Delay_ms(2000);

    /* 测试电路识别 */
    TJC_HMI_HandleStartIdentify();         // 进入识别界面
    Delay_ms(1000);
    TJC_HMI_HandleStartSweep();            // 开始扫频识别
    Delay_ms(3000);
    TJC_HMI_HandleReturnMain();            // 返回主界面
}

/**
 * @brief  设置t2控件 - 输出频率显示 (实时显示，带单位)
 * @param  frequency_hz: 频率值 (Hz)
 * @retval None
 */
void TJC_HMI_SetFrequencyDisplay(uint32_t frequency_hz)
{
    char freq_display[32];

    current_frequency_hz = frequency_hz;
    UNUSED(current_frequency_hz); // 避免警告，保留变量供将来使用

    /* 根据频率大小选择合适的单位和显示格式 */
    if (frequency_hz >= 1000000) {
        /* MHz显示 */
        double freq_mhz = (double)frequency_hz / 1000000.0;
        snprintf(freq_display, sizeof(freq_display), "%.3f MHz", freq_mhz);
    } else if (frequency_hz >= 1000) {
        /* kHz显示 */
        double freq_khz = (double)frequency_hz / 1000.0;
        snprintf(freq_display, sizeof(freq_display), "%.1f kHz", freq_khz);
    } else {
        /* Hz显示 */
        snprintf(freq_display, sizeof(freq_display), "%lu Hz", frequency_hz);
    }

    /* 更新t2控件显示 */
    TJC_HMI_SetText(TJC_CTRL_T2, freq_display);
}

/**
 * @brief  设置t4控件 - 模拟电路电压输出值 (考虑增益后的峰峰值)
 * @param  voltage_mv: 电压峰峰值 (mV)
 * @retval None
 */
void TJC_HMI_SetVoltageDisplay(uint16_t voltage_mv)
{
    char voltage_display[32];

    current_voltage_mv = voltage_mv;
    UNUSED(current_voltage_mv); // 避免警告，保留变量供将来使用

    /* 根据电压大小选择合适的单位和显示格式 */
    if (voltage_mv >= 1000) {
        /* V显示 */
        double voltage_v = (double)voltage_mv / 1000.0;
        snprintf(voltage_display, sizeof(voltage_display), "%.2f V", voltage_v);
    } else {
        /* mV显示 */
        snprintf(voltage_display, sizeof(voltage_display), "%d mV", voltage_mv);
    }

    /* 更新t4控件显示 */
    TJC_HMI_SetText(TJC_CTRL_T4, voltage_display);
}

/**
 * @brief  设置t6控件 - 滤波类型显示
 * @param  filter_type: 滤波类型字符串 ("等待" 或 具体类型)
 * @retval None
 */
void TJC_HMI_SetFilterTypeDisplay(const char* filter_type)
{
    /* 更新t6控件显示 */
    TJC_HMI_SetText(TJC_CTRL_T6, filter_type);
}

/**
 * @brief  处理n0控件数字键盘输入
 * @param  value: 输入的数值
 * @retval None
 */
void TJC_HMI_HandleNumberInput(uint32_t value)
{
    n0_input_value = value;

    /* 根据当前单位计算实际频率 */
    uint32_t frequency_hz = TJC_ConvertToHz(value, t0_input_unit);

    /* 设置AD9910频率 */
    if (frequency_hz >= 1 && frequency_hz <= 420000000) { // AD9910频率范围
        ControlStatus_t status = AD9910_Control_SetFrequency(frequency_hz);
        if (status == CTRL_STATUS_OK) {
            /* 更新t2频率显示 */
            TJC_HMI_SetFrequencyDisplay(frequency_hz);

            /* 重新计算并更新t4电路电压显示 */
            SystemParams_t params;
            if (AD9910_Control_GetParams(&params) == CTRL_STATUS_OK) {
                TJC_HMI_SetVoltageDisplay(params.target_amplitude_mv);
            }
        }
    }
}

/**
 * @brief  处理t0控件全键盘输入 (单位设置，大小写不敏感)
 * @param  unit_text: 输入的单位文本
 * @retval None
 */
void TJC_HMI_HandleUnitInput(const char* unit_text)
{
    char normalized_unit[16];

    /* 解析并标准化单位 (大小写不敏感) */
    TJC_ParseUnitString(unit_text, normalized_unit);

    /* 转换为大写存储 */
    for (int i = 0; normalized_unit[i]; i++) {
        t0_input_unit[i] = toupper(normalized_unit[i]);
    }
    t0_input_unit[strlen(normalized_unit)] = '\0';

    /* 更新t0控件显示 (保持用户输入的格式) */
    TJC_HMI_SetText(TJC_CTRL_T0, normalized_unit);

    /* 如果已有输入数值，重新计算频率 */
    if (n0_input_value > 0) {
        TJC_HMI_HandleNumberInput(n0_input_value);
    }
}

/**
 * @brief  处理b0按钮事件
 * @param  page_id: 当前页面 (0=Page0测量功能, 1=Page1页面切换)
 * @retval None
 */
void TJC_HMI_HandleButtonB0(uint8_t page_id)
{
    if (page_id == 0) {
        /* 在Page0，切换到Page1并开始测量 */
        TJC_SendString("page 1");
        TJC_SendEndSequence();
        current_page = 1;
        Delay_ms(100);

        /* 初始化Page1显示 */
        TJC_HMI_SetFilterTypeDisplay("等待");

        /* 发送测量开始信号 (printh 31) */
        TJC_SendString("printh 31");
        TJC_SendEndSequence();

    } else if (page_id == 1) {
        /* 在Page1，切换回Page0 */
        TJC_SendString("page 0");
        TJC_SendEndSequence();
        current_page = 0;
    }
}

/**
 * @brief  开始测量未知电路 (响应printh 31事件)
 * @param  None
 * @retval None
 */
void TJC_HMI_StartMeasurement(void)
{
    measurement_active = true;

    /* 设置t6控件为等待状态 */
    TJC_HMI_SetFilterTypeDisplay("等待");

    /* 这里可以添加实际的测量逻辑 */
    /* TODO: 调用滤波器识别算法 */
}

/* ==================== 兼容性函数 ==================== */

/**
 * @brief  兼容性函数 - 设置输出频率显示
 * @param  frequency_hz: 频率值 (Hz)
 * @retval None
 */
void TJC_HMI_SetOutputFrequency(uint32_t frequency_hz)
{
    TJC_HMI_SetFrequencyDisplay(frequency_hz);
}

/**
 * @brief  兼容性函数 - 设置电路电压输出峰峰值
 * @param  voltage_mv: 电压峰峰值 (mV)
 * @retval None
 */
void TJC_HMI_SetCircuitVoltage(uint16_t voltage_mv)
{
    TJC_HMI_SetVoltageDisplay(voltage_mv);
}

/**
 * @brief  兼容性函数 - 设置滤波类型显示
 * @param  filter_type: 滤波类型字符串
 * @retval None
 */
void TJC_HMI_SetFilterType(const char* filter_type)
{
    TJC_HMI_SetFilterTypeDisplay(filter_type);
}

/* ==================== 测试和调试函数 ==================== */

/**
 * @brief  测试陶晶池串口屏所有功能
 * @param  None
 * @retval None
 */
void TJC_HMI_TestAllFunctions(void)
{
    /* 测试Page0显示 */
    TJC_HMI_ShowWelcome();
    Delay_ms(1000);

    /* 测试频率显示 */
    TJC_HMI_SetFrequencyDisplay(1000000);  // 1MHz
    Delay_ms(500);
    TJC_HMI_SetFrequencyDisplay(5000000);  // 5MHz
    Delay_ms(500);

    /* 测试电压显示 */
    TJC_HMI_SetVoltageDisplay(300);        // 300mV
    Delay_ms(500);
    TJC_HMI_SetVoltageDisplay(500);        // 500mV
    Delay_ms(500);

    /* 测试数字输入 */
    TJC_HMI_HandleNumberInput(10);         // 输入10
    Delay_ms(500);

    /* 测试单位输入 */
    TJC_HMI_HandleUnitInput("mhz");        // 输入MHz (小写)
    Delay_ms(500);
    TJC_HMI_HandleUnitInput("KHz");        // 输入kHz (混合大小写)
    Delay_ms(500);

    /* 测试页面切换 */
    TJC_HMI_HandleButtonB0(0);             // 从Page0切换到Page1
    Delay_ms(1000);
    TJC_HMI_HandleButtonB0(1);             // 从Page1切换回Page0
}

    /* 这里可以添加实际的测量逻辑 */
    /* 例如：启动频率扫描、幅度测量等 */

    /* 模拟测量完成后的结果显示 */
    // TJC_HMI_SetFilterType("Low Pass Filter");
}

/**
 * @brief  更新所有显示参数
 * @param  None
 * @retval None
 */
void TJC_HMI_UpdateAllParameters(void)
{
    SystemParams_t params;

    if (AD9910_Control_GetParams(&params) == CTRL_STATUS_OK) {
        /* 更新频率显示 */
        TJC_HMI_SetOutputFrequency(params.frequency_hz);

        /* 计算并更新电路电压输出 (考虑增益和传递函数) */
        GainResult_t gain_result;
        GainConfig_t gain_config = {
            .frequency_hz = params.frequency_hz,
            .input_amplitude_mv = params.target_amplitude_mv,
            .temperature_c = 25.0,
            .enable_nonlinear_correction = true
        };

        if (GainCalculator_Calculate(&gain_config, &gain_result)) {
            uint16_t circuit_voltage = (uint16_t)(params.target_amplitude_mv * gain_result.calculated_gain);
            TJC_HMI_SetCircuitVoltage(circuit_voltage);
        } else {
            TJC_HMI_SetCircuitVoltage(0);
        }
    }
}

/* ==================== 缺失函数实现 ==================== */

/**
 * @brief  发送命令结束序列
 * @param  None
 * @retval None
 */
static void TJC_SendEndSequence(void)
{
    uint8_t end_bytes[] = TJC_CMD_END_BYTES;
    for (int i = 0; i < sizeof(end_bytes); i++) {
        TJC_SendByte(end_bytes[i]);
    }
}

/**
 * @brief  发送命令结束序列 (公共接口)
 * @param  None
 * @retval None
 */
void TJC_SendEndSequence(void)
{
    uint8_t end_bytes[] = TJC_CMD_END_BYTES;
    for (int i = 0; i < sizeof(end_bytes); i++) {
        TJC_SendByte(end_bytes[i]);
    }
}

/**
 * @brief  显示欢迎界面
 * @param  None
 * @retval None
 */
void TJC_HMI_ShowWelcome(void)
{
    TJC_HMI_ShowMainPage();
}

/**
 * @brief  设置滤波类型显示
 * @param  filter_type: 滤波类型字符串
 * @retval None
 */
void TJC_HMI_SetFilterTypeDisplay(const char* filter_type)
{
    TJC_HMI_SetText(TJC_CTRL_T6, filter_type);
}

/**
 * @brief  处理测量按钮
 * @param  page_id: 页面ID
 * @retval None
 */
void TJC_HMI_HandleMeasureButton(uint8_t page_id)
{
    if (page_id == 0) {
        /* 在Page0，切换到Page1并开始测量 */
        TJC_SendString("page 1");
        TJC_SendEndSequence();
        current_page = 1;
        Delay_ms(100);

        /* 初始化Page1显示 */
        TJC_HMI_SetFilterTypeDisplay("Waiting");

        /* 开始测量 */
        TJC_HMI_StartMeasurement();

    } else if (page_id == 1) {
        /* 在Page1，切换回Page0 */
        TJC_SendString("page 0");
        TJC_SendEndSequence();
        current_page = 0;
    }
}

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
