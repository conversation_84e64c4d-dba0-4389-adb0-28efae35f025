/**
  ******************************************************************************
  * @file    tjc_hmi.h
  * <AUTHOR>
  * @version V1.0
  * @date    2024-08-02
  * @brief   陶晶池串口屏驱动头文件
  ******************************************************************************
  * @attention
  *
  * 本文件实现陶晶池串口屏的通信功能，包括：
  * - 串口通信协议
  * - 控件数据更新
  * - 触摸事件处理
  * - AD9910参数显示
  *
  * 陶晶池串口屏特点:
  * - 指令格式: 指令内容 + 0xFF 0xFF 0xFF
  * - 波特率: 115200 (默认)
  * - 数据位: 8位，停止位: 1位，校验位: 无
  * - 支持触摸返回事件
  *
  * 硬件连接:
  * PA9  -> TJC_TX (串口屏RX)
  * PA10 -> TJC_RX (串口屏TX)
  *
  ******************************************************************************
  */

#ifndef __TJC_HMI_H
#define __TJC_HMI_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx.h"
#include <stdint.h>
#include <stdbool.h>

/* Exported types ------------------------------------------------------------*/

/**
 * @brief  陶晶池串口屏事件类型
 */
typedef enum {
    TJC_EVENT_NONE = 0,        ///< 无事件
    TJC_EVENT_TOUCH_PRESS,     ///< 触摸按下
    TJC_EVENT_TOUCH_RELEASE,   ///< 触摸释放
    TJC_EVENT_PAGE_CHANGE,     ///< 页面切换
    TJC_EVENT_SLIDER_CHANGE,   ///< 滑块变化
    TJC_EVENT_TEXT_INPUT       ///< 文本输入
} TJC_EventType_t;

/**
 * @brief  陶晶池串口屏事件结构体
 */
typedef struct {
    TJC_EventType_t type;      ///< 事件类型
    uint8_t page_id;           ///< 页面ID
    uint8_t component_id;      ///< 控件ID
    uint32_t value;            ///< 事件值
    char text[32];             ///< 文本内容 (文本输入事件)
} TJC_Event_t;

/**
 * @brief  陶晶池串口屏回调函数类型
 */
typedef void (*TJC_EventCallback_t)(TJC_Event_t* event);

/* Exported constants --------------------------------------------------------*/

/* 串口配置 - 适配嘉立创天空星STM32F407VGT6 */
#define TJC_USART                   USART2
#define TJC_USART_RCC               RCC_APB1Periph_USART2
#define TJC_USART_TX_RCC            RCC_AHB1Periph_GPIOA
#define TJC_USART_RX_RCC            RCC_AHB1Periph_GPIOA

#define TJC_USART_TX_PORT           GPIOA
#define TJC_USART_TX_PIN            GPIO_Pin_2     // PA2 (USART2_TX)
#define TJC_USART_RX_PORT           GPIOA
#define TJC_USART_RX_PIN            GPIO_Pin_3     // PA3 (USART2_RX)
#define TJC_USART_AF                GPIO_AF_USART2
#define TJC_USART_TX_AF_PIN         GPIO_PinSource2
#define TJC_USART_RX_AF_PIN         GPIO_PinSource3

/* 通信配置 */
#define TJC_BAUD_RATE               115200      ///< 波特率
#define TJC_RX_BUFFER_SIZE          256         ///< 接收缓冲区大小
#define TJC_CMD_END_BYTES           {0xFF, 0xFF, 0xFF}  ///< 命令结束符

/* 页面定义 */
#define TJC_PAGE_MAIN               0           ///< 主页面 (page0)
#define TJC_PAGE_MEASURE            1           ///< 测量页面 (page1)

/* ==================== 电赛G题专用HMI界面配置 ==================== */

/* Page0 - 主控制界面控件定义 */
#define TJC_CTRL_TITLE              "t0"        ///< 标题文本
#define TJC_CTRL_FREQ_INPUT         "n0"        ///< 频率数值输入
#define TJC_CTRL_FREQ_UNIT          "t1"        ///< 频率单位显示
#define TJC_CTRL_FREQ_CURRENT       "t2"        ///< 当前频率显示
#define TJC_CTRL_AMP_INPUT          "n1"        ///< 幅度数值输入
#define TJC_CTRL_AMP_UNIT           "t3"        ///< 幅度单位显示
#define TJC_CTRL_AMP_CURRENT        "t4"        ///< 当前幅度显示
#define TJC_CTRL_BTN_APPLY          "b0"        ///< 应用设置按钮
#define TJC_CTRL_BTN_IDENTIFY       "b1"        ///< 识别电路按钮
#define TJC_CTRL_BTN_PRESET         "b2"        ///< 预设参数按钮

/* Page1 - 电路识别界面控件定义 */
#define TJC_CTRL_ID_TITLE           "t5"        ///< 识别界面标题
#define TJC_CTRL_SWEEP_START        "n2"        ///< 扫频起始频率
#define TJC_CTRL_SWEEP_END          "n3"        ///< 扫频结束频率
#define TJC_CTRL_SWEEP_UNIT         "t6"        ///< 扫频单位显示
#define TJC_CTRL_SWEEP_POINTS       "n4"        ///< 扫频点数
#define TJC_CTRL_SWEEP_TIME         "n5"        ///< 步进时间
#define TJC_CTRL_ID_STATUS          "t7"        ///< 识别状态显示
#define TJC_CTRL_CIRCUIT_TYPE       "t8"        ///< 电路类型显示
#define TJC_CTRL_MODEL_PARAMS       "t9"        ///< 模型参数显示
#define TJC_CTRL_BTN_START          "b3"        ///< 开始识别按钮
#define TJC_CTRL_BTN_STOP           "b4"        ///< 停止识别按钮
#define TJC_CTRL_BTN_RETURN         "b5"        ///< 返回主页按钮

/* 页面定义 */
#define TJC_PAGE_MAIN               0           ///< 主控制页面
#define TJC_PAGE_IDENTIFY           1           ///< 电路识别页面

/* 按钮ID定义 */
#define TJC_ID_B0                   0           ///< 按钮b0的ID
#define TJC_ID_B1                   1           ///< 按钮b1的ID
#define TJC_ID_B2                   2           ///< 按钮b2的ID
#define TJC_ID_B3                   3           ///< 按钮b3的ID
#define TJC_ID_B4                   4           ///< 按钮b4的ID
#define TJC_ID_B5                   5           ///< 按钮b5的ID

/* 兼容性控件定义 */
#define TJC_CTRL_T0                 "t0"        ///< 文本控件t0
#define TJC_CTRL_T2                 "t2"        ///< 文本控件t2
#define TJC_CTRL_T4                 "t4"        ///< 文本控件t4
#define TJC_CTRL_T6                 "t6"        ///< 文本控件t6

/* 按钮事件码定义 */
#define TJC_EVENT_APPLY_SETTINGS    10          ///< 应用设置事件
#define TJC_EVENT_START_IDENTIFY    11          ///< 开始识别事件
#define TJC_EVENT_PRESET_PARAMS     12          ///< 预设参数事件
#define TJC_EVENT_START_SWEEP       13          ///< 开始扫频事件
#define TJC_EVENT_STOP_SWEEP        14          ///< 停止扫频事件
#define TJC_EVENT_RETURN_MAIN       15          ///< 返回主页事件

/* Exported functions --------------------------------------------------------*/

/**
 * @brief  陶晶池串口屏初始化
 * @param  None
 * @retval None
 */
void TJC_HMI_Init(void);

/**
 * @brief  陶晶池串口屏处理 (主循环调用)
 * @param  None
 * @retval None
 */
void TJC_HMI_Process(void);

/**
 * @brief  发送命令到陶晶池串口屏
 * @param  cmd: 命令字符串
 * @retval None
 */
void TJC_HMI_SendCommand(const char* cmd);

/**
 * @brief  设置文本控件内容
 * @param  obj_name: 控件名称
 * @param  text: 文本内容
 * @retval None
 */
void TJC_HMI_SetText(const char* obj_name, const char* text);

/**
 * @brief  设置数值控件内容
 * @param  obj_name: 控件名称
 * @param  value: 数值
 * @retval None
 */
void TJC_HMI_SetValue(const char* obj_name, int32_t value);

/**
 * @brief  设置进度条/滑块值
 * @param  obj_name: 控件名称
 * @param  value: 进度值 (0-100)
 * @retval None
 */
void TJC_HMI_SetProgress(const char* obj_name, uint8_t value);

/**
 * @brief  切换页面
 * @param  page_id: 页面ID
 * @retval None
 */
void TJC_HMI_ChangePage(uint8_t page_id);

/**
 * @brief  设置控件可见性
 * @param  obj_name: 控件名称
 * @param  visible: true-可见, false-隐藏
 * @retval None
 */
void TJC_HMI_SetVisible(const char* obj_name, bool visible);

/**
 * @brief  设置控件颜色
 * @param  obj_name: 控件名称
 * @param  color: 颜色值 (RGB565)
 * @retval None
 */
void TJC_HMI_SetColor(const char* obj_name, uint16_t color);

/**
 * @brief  注册事件回调函数
 * @param  callback: 回调函数指针
 * @retval None
 */
void TJC_HMI_RegisterCallback(TJC_EventCallback_t callback);

/* ==================== AD9910专用显示函数 ==================== */

/* ==================== 电赛G题专用HMI接口函数 ==================== */

/**
 * @brief  初始化HMI并显示主控制界面
 * @param  None
 * @retval None
 */
void TJC_HMI_ShowMainPage(void);

/**
 * @brief  显示电路识别界面
 * @param  None
 * @retval None
 */
void TJC_HMI_ShowIdentifyPage(void);

/**
 * @brief  设置当前频率显示
 * @param  frequency_hz: 频率值 (Hz)
 * @retval None
 */
void TJC_HMI_SetCurrentFrequency(uint32_t frequency_hz);

/**
 * @brief  设置当前幅度显示
 * @param  amplitude_mv: 幅度值 (mV)
 * @retval None
 */
void TJC_HMI_SetCurrentAmplitude(uint16_t amplitude_mv);

/**
 * @brief  设置识别状态显示
 * @param  status: 状态文本
 * @retval None
 */
void TJC_HMI_SetIdentifyStatus(const char* status);

/**
 * @brief  设置电路类型显示
 * @param  circuit_type: 电路类型文本
 * @retval None
 */
void TJC_HMI_SetCircuitType(const char* circuit_type);

/**
 * @brief  设置模型参数显示
 * @param  model_params: 模型参数文本
 * @retval None
 */
void TJC_HMI_SetModelParams(const char* model_params);

/**
 * @brief  处理频率输入
 * @param  frequency_value: 频率数值
 * @retval None
 */
void TJC_HMI_HandleFrequencyInput(uint32_t frequency_value);

/**
 * @brief  处理幅度输入
 * @param  amplitude_value: 幅度数值
 * @retval None
 */
void TJC_HMI_HandleAmplitudeInput(uint16_t amplitude_value);

/**
 * @brief  处理应用设置按钮
 * @param  None
 * @retval None
 */
void TJC_HMI_HandleApplySettings(void);

/**
 * @brief  处理开始识别按钮
 * @param  None
 * @retval None
 */
void TJC_HMI_HandleStartIdentify(void);

/**
 * @brief  处理预设参数按钮
 * @param  None
 * @retval None
 */
void TJC_HMI_HandlePresetParams(void);

/**
 * @brief  处理开始扫频按钮
 * @param  None
 * @retval None
 */
void TJC_HMI_HandleStartSweep(void);

/**
 * @brief  处理停止扫频按钮
 * @param  None
 * @retval None
 */
void TJC_HMI_HandleStopSweep(void);

/**
 * @brief  处理返回主页按钮
 * @param  None
 * @retval None
 */
void TJC_HMI_HandleReturnMain(void);

/**
 * @brief  更新所有显示参数
 * @param  None
 * @retval None
 */
void TJC_HMI_UpdateAllParameters(void);

/**
 * @brief  获取系统运行时间
 * @param  None
 * @retval 时间戳 (ms)
 */
uint32_t TJC_HMI_GetSystemTime(void);

/**
 * @brief  测试陶晶池串口屏所有功能
 * @param  None
 * @retval None
 */
void TJC_HMI_TestAllFunctions(void);

/* ==================== 兼容性函数声明 ==================== */

/**
 * @brief  兼容性函数 - 设置输出频率显示
 * @param  frequency_hz: 频率值 (Hz)
 * @retval None
 */
void TJC_HMI_SetOutputFrequency(uint32_t frequency_hz);

/**
 * @brief  兼容性函数 - 设置电路电压输出峰峰值
 * @param  voltage_mv: 电压峰峰值 (mV)
 * @retval None
 */
void TJC_HMI_SetCircuitVoltage(uint16_t voltage_mv);

/**
 * @brief  兼容性函数 - 设置滤波类型显示
 * @param  filter_type: 滤波类型字符串
 * @retval None
 */
void TJC_HMI_SetFilterType(const char* filter_type);

/**
 * @brief  显示欢迎界面
 * @param  None
 * @retval None
 */
void TJC_HMI_ShowWelcome(void);

/**
 * @brief  设置电压显示
 * @param  voltage_mv: 电压值 (mV)
 * @retval None
 */
void TJC_HMI_SetVoltageDisplay(uint16_t voltage_mv);

/**
 * @brief  设置滤波类型显示
 * @param  filter_type: 滤波类型字符串
 * @retval None
 */
void TJC_HMI_SetFilterTypeDisplay(const char* filter_type);

/**
 * @brief  处理数字输入
 * @param  value: 输入值
 * @retval None
 */
void TJC_HMI_HandleNumberInput(uint32_t value);

/**
 * @brief  处理单位输入
 * @param  unit_text: 单位文本
 * @retval None
 */
void TJC_HMI_HandleUnitInput(const char* unit_text);

/**
 * @brief  开始测量
 * @param  None
 * @retval None
 */
void TJC_HMI_StartMeasurement(void);

/**
 * @brief  处理测量按钮
 * @param  page_id: 页面ID
 * @retval None
 */
void TJC_HMI_HandleMeasureButton(uint8_t page_id);

/**
 * @brief  发送命令结束序列
 * @param  None
 * @retval None
 */
void TJC_SendEndSequence(void);

/**
 * @brief  串口中断处理函数
 * @param  None
 * @retval None
 */
void TJC_HMI_UART_IRQHandler(void);

/**
 * @brief  获取是否有新事件
 * @param  event: 事件输出
 * @retval true-有事件, false-无事件
 */
bool TJC_HMI_HasEvent(TJC_Event_t* event);

/**
 * @brief  清空事件队列
 * @param  None
 * @retval None
 */
void TJC_HMI_ClearEvents(void);

#ifdef __cplusplus
}
#endif

#endif /* __TJC_HMI_H */

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
